# Пример подписки в JSON

```json
[
  {
    "dns": {
      "queryStrategy": "UseIP",
      "servers": [
        {
          "address": "*******",
          "skipFallback": false
        }
      ],
      "tag": "dns_out"
    },
    "inbounds": [
      {
        "port": 10808,
        "protocol": "mixed",
        "settings": {
          "auth": "noauth",
          "udp": true,
          "userLevel": 8
        },
        "sniffing": {
          "destOverride": [
            "http",
            "tls",
            "quic",
            "fakedns"
          ],
          "enabled": true
        },
        "tag": "mixed"
      },
      {
        "port": 10809,
        "protocol": "http",
        "settings": {
          "userLevel": 8
        },
        "tag": "http"
      }
    ],
    "log": {
      "loglevel": "warning"
    },
    "outbounds": [
      {
        "protocol": "vless",
        "tag": "proxy",
        "streamSettings": {
          "network": "tcp",
          "realitySettings": {
            "fingerprint": "chrome",
            "mldsa65Verify": "",
            "publicKey": "Ex0EgHtdE54vF1w3ZE2iTEA-7Pu1cl36nrETaRn4nm0",
            "serverName": "gw4.smartvpn.vip",
            "shortId": "3f",
            "show": false,
            "spiderX": "/1z1dtEipwLrHncZ"
          },
          "security": "reality",
          "sockopt": {
            "dialerProxy": "fragment",
            "tcpKeepAliveIdle": 100,
            "tcpMptcp": true,
            "penetrate": true
          },
          "tcpSettings": {
            "header": {
              "type": "none"
            }
          }
        },
        "mux": {
          "enabled": true,
          "concurrency": 4,
          "xudpConcurrency": 8,
          "xudpProxyUDP443": "allow"
        },
        "settings": {
          "address": "**************",
          "encryption": "none",
          "flow": "xtls-rprx-vision",
          "id": "0198938d-6ce8-7203-83b1-fdd90ad58233",
          "port": 443
        }
      },
      {
        "protocol": "freedom",
        "settings": {
          "domainStrategy": "AsIs",
          "noises": [],
          "redirect": ""
        },
        "tag": "direct"
      },
      {
        "protocol": "blackhole",
        "settings": {
          "response": {
            "type": "http"
          }
        },
        "tag": "block"
      },
      {
        "tag": "fragment",
        "protocol": "freedom",
        "settings": {
          "domainStrategy": "AsIs",
          "fragment": {
            "packets": "tlshello",
            "length": "100-200",
            "interval": "10-20",
            "maxSplit": "300-400"
          }
        },
        "streamSettings": {
          "sockopt": {
            "tcpKeepAliveIdle": 100,
            "tcpMptcp": true,
            "penetrate": true
          }
        }
      },
      {
        "tag": "noises",
        "protocol": "freedom",
        "settings": {
          "domainStrategy": "AsIs",
          "noises": [
            {
              "type": "rand",
              "packet": "10-20",
              "delay": "10-16",
              "applyTo": "ip"
            }
          ]
        }
      }
    ],
    "policy": {
      "levels": {
        "8": {
          "connIdle": 300,
          "downlinkOnly": 1,
          "handshake": 4,
          "uplinkOnly": 1
        }
      },
      "system": {
        "statsOutboundDownlink": true,
        "statsOutboundUplink": true
      }
    },
    "remarks": "🇳🇱 Nederland - для WiFi и LTE-client-ibg9bgf9@vpn.local_1",
    "routing": {
      "domainStrategy": "AsIs",
      "rules": [
        {
          "network": "tcp,udp",
          "outboundTag": "proxy",
          "type": "field"
        }
      ]
    },
    "stats": {}
  },
  {
    "dns": {
      "queryStrategy": "UseIP",
      "servers": [
        {
          "address": "*******",
          "skipFallback": false
        }
      ],
      "tag": "dns_out"
    },
    "inbounds": [
      {
        "port": 10808,
        "protocol": "mixed",
        "settings": {
          "auth": "noauth",
          "udp": true,
          "userLevel": 8
        },
        "sniffing": {
          "destOverride": [
            "http",
            "tls",
            "quic",
            "fakedns"
          ],
          "enabled": true
        },
        "tag": "mixed"
      },
      {
        "port": 10809,
        "protocol": "http",
        "settings": {
          "userLevel": 8
        },
        "tag": "http"
      }
    ],
    "log": {
      "loglevel": "warning"
    },
    "outbounds": [
      {
        "protocol": "vless",
        "tag": "proxy",
        "streamSettings": {
          "network": "xhttp",
          "security": "tls",
          "sockopt": {
            "dialerProxy": "fragment",
            "tcpKeepAliveIdle": 100,
            "tcpMptcp": true,
            "penetrate": true
          },
          "tlsSettings": {
            "alpn": [],
            "fingerprint": "chrome",
            "serverName": "gw2.smartvpn.vip"
          },
          "xhttpSettings": {
            "headers": {},
            "host": "gw2.smartvpn.vip",
            "mode": "packet-up",
            "noSSEHeader": false,
            "path": "/",
            "scMaxBufferedPosts": 20,
            "scMaxEachPostBytes": "1000000",
            "scStreamUpServerSecs": "20-80",
            "seqKey": "",
            "seqPlacement": "",
            "sessionKey": "",
            "sessionPlacement": "",
            "uplinkChunkSize": 0,
            "uplinkDataKey": "",
            "uplinkDataPlacement": "",
            "uplinkHTTPMethod": "",
            "xPaddingBytes": "100-1000",
            "xPaddingHeader": "",
            "xPaddingKey": "",
            "xPaddingMethod": "",
            "xPaddingObfsMode": false,
            "xPaddingPlacement": ""
          }
        },
        "mux": {
          "enabled": true,
          "concurrency": 4,
          "xudpConcurrency": 8,
          "xudpProxyUDP443": "allow"
        },
        "settings": {
          "address": "gw2.smartvpn.vip",
          "encryption": "none",
          "id": "0198938d-6ce8-7203-83b1-fdd90ad58233",
          "port": 443
        }
      },
      {
        "protocol": "freedom",
        "settings": {
          "domainStrategy": "AsIs",
          "noises": [],
          "redirect": ""
        },
        "tag": "direct"
      },
      {
        "protocol": "blackhole",
        "settings": {
          "response": {
            "type": "http"
          }
        },
        "tag": "block"
      },
      {
        "tag": "fragment",
        "protocol": "freedom",
        "settings": {
          "domainStrategy": "AsIs",
          "fragment": {
            "packets": "tlshello",
            "length": "100-200",
            "interval": "10-20",
            "maxSplit": "300-400"
          }
        },
        "streamSettings": {
          "sockopt": {
            "tcpKeepAliveIdle": 100,
            "tcpMptcp": true,
            "penetrate": true
          }
        }
      },
      {
        "tag": "noises",
        "protocol": "freedom",
        "settings": {
          "domainStrategy": "AsIs",
          "noises": [
            {
              "type": "rand",
              "packet": "10-20",
              "delay": "10-16",
              "applyTo": "ip"
            }
          ]
        }
      }
    ],
    "policy": {
      "levels": {
        "8": {
          "connIdle": 300,
          "downlinkOnly": 1,
          "handshake": 4,
          "uplinkOnly": 1
        }
      },
      "system": {
        "statsOutboundDownlink": true,
        "statsOutboundUplink": true
      }
    },
    "remarks": "🔓 Антиблок, только для LTE-client-ibg9bgf9@vpn.local_5",
    "routing": {
      "domainStrategy": "AsIs",
      "rules": [
        {
          "network": "tcp,udp",
          "outboundTag": "proxy",
          "type": "field"
        }
      ]
    },
    "stats": {}
  },
  {
    "dns": {
      "queryStrategy": "UseIP",
      "servers": [
        {
          "address": "*******",
          "skipFallback": false
        }
      ],
      "tag": "dns_out"
    },
    "inbounds": [
      {
        "port": 10808,
        "protocol": "mixed",
        "settings": {
          "auth": "noauth",
          "udp": true,
          "userLevel": 8
        },
        "sniffing": {
          "destOverride": [
            "http",
            "tls",
            "quic",
            "fakedns"
          ],
          "enabled": true
        },
        "tag": "mixed"
      },
      {
        "port": 10809,
        "protocol": "http",
        "settings": {
          "userLevel": 8
        },
        "tag": "http"
      }
    ],
    "log": {
      "loglevel": "warning"
    },
    "outbounds": [
      {
        "protocol": "vless",
        "tag": "proxy",
        "streamSettings": {
          "network": "xhttp",
          "security": "tls",
          "sockopt": {
            "dialerProxy": "fragment",
            "tcpKeepAliveIdle": 100,
            "tcpMptcp": true,
            "penetrate": true
          },
          "tlsSettings": {
            "alpn": [
              "h2",
              "http/1.1",
              "h3"
            ],
            "fingerprint": "chrome",
            "serverName": "gw4.smartvpn.vip"
          },
          "xhttpSettings": {
            "headers": {},
            "host": "gw4.smartvpn.vip",
            "mode": "auto",
            "noSSEHeader": false,
            "path": "/",
            "scMaxBufferedPosts": 30,
            "scMaxEachPostBytes": "1000000",
            "scStreamUpServerSecs": "20-80",
            "seqKey": "",
            "seqPlacement": "",
            "sessionKey": "",
            "sessionPlacement": "",
            "uplinkChunkSize": 0,
            "uplinkDataKey": "",
            "uplinkDataPlacement": "",
            "uplinkHTTPMethod": "",
            "xPaddingBytes": "100-1000",
            "xPaddingHeader": "",
            "xPaddingKey": "",
            "xPaddingMethod": "",
            "xPaddingObfsMode": false,
            "xPaddingPlacement": ""
          }
        },
        "mux": {
          "enabled": true,
          "concurrency": 4,
          "xudpConcurrency": 8,
          "xudpProxyUDP443": "allow"
        },
        "settings": {
          "address": "gw4.smartvpn.vip",
          "encryption": "none",
          "id": "0198938d-6ce8-7203-83b1-fdd90ad58233",
          "port": 8443
        }
      },
      {
        "protocol": "freedom",
        "settings": {
          "domainStrategy": "AsIs",
          "noises": [],
          "redirect": ""
        },
        "tag": "direct"
      },
      {
        "protocol": "blackhole",
        "settings": {
          "response": {
            "type": "http"
          }
        },
        "tag": "block"
      },
      {
        "tag": "fragment",
        "protocol": "freedom",
        "settings": {
          "domainStrategy": "AsIs",
          "fragment": {
            "packets": "tlshello",
            "length": "100-200",
            "interval": "10-20",
            "maxSplit": "300-400"
          }
        },
        "streamSettings": {
          "sockopt": {
            "tcpKeepAliveIdle": 100,
            "tcpMptcp": true,
            "penetrate": true
          }
        }
      },
      {
        "tag": "noises",
        "protocol": "freedom",
        "settings": {
          "domainStrategy": "AsIs",
          "noises": [
            {
              "type": "rand",
              "packet": "10-20",
              "delay": "10-16",
              "applyTo": "ip"
            }
          ]
        }
      }
    ],
    "policy": {
      "levels": {
        "8": {
          "connIdle": 300,
          "downlinkOnly": 1,
          "handshake": 4,
          "uplinkOnly": 1
        }
      },
      "system": {
        "statsOutboundDownlink": true,
        "statsOutboundUplink": true
      }
    },
    "remarks": "🇳🇱 Nederland - запасной #1 для WiFi-client-ibg9bgf9@vpn.local_12",
    "routing": {
      "domainStrategy": "AsIs",
      "rules": [
        {
          "network": "tcp,udp",
          "outboundTag": "proxy",
          "type": "field"
        }
      ]
    },
    "stats": {}
  },
  {
    "dns": {
      "queryStrategy": "UseIP",
      "servers": [
        {
          "address": "*******",
          "skipFallback": false
        }
      ],
      "tag": "dns_out"
    },
    "inbounds": [
      {
        "port": 10808,
        "protocol": "mixed",
        "settings": {
          "auth": "noauth",
          "udp": true,
          "userLevel": 8
        },
        "sniffing": {
          "destOverride": [
            "http",
            "tls",
            "quic",
            "fakedns"
          ],
          "enabled": true
        },
        "tag": "mixed"
      },
      {
        "port": 10809,
        "protocol": "http",
        "settings": {
          "userLevel": 8
        },
        "tag": "http"
      }
    ],
    "log": {
      "loglevel": "warning"
    },
    "outbounds": [
      {
        "protocol": "vless",
        "tag": "proxy",
        "streamSettings": {
          "network": "ws",
          "security": "tls",
          "sockopt": {
            "dialerProxy": "fragment",
            "tcpKeepAliveIdle": 100,
            "tcpMptcp": true,
            "penetrate": true
          },
          "tlsSettings": {
            "alpn": [
              "http/1.1",
              "h2",
              "h3"
            ],
            "fingerprint": "chrome",
            "serverName": "gw4.smartvpn.vip"
          },
          "wsSettings": {
            "headers": {},
            "heartbeatPeriod": 0,
            "host": "",
            "path": "/"
          }
        },
        "mux": {
          "enabled": true,
          "concurrency": 4,
          "xudpConcurrency": 8,
          "xudpProxyUDP443": "allow"
        },
        "settings": {
          "address": "**************",
          "encryption": "none",
          "id": "0198938d-6ce8-7203-83b1-fdd90ad58233",
          "port": 8080
        }
      },
      {
        "protocol": "freedom",
        "settings": {
          "domainStrategy": "AsIs",
          "noises": [],
          "redirect": ""
        },
        "tag": "direct"
      },
      {
        "protocol": "blackhole",
        "settings": {
          "response": {
            "type": "http"
          }
        },
        "tag": "block"
      },
      {
        "tag": "fragment",
        "protocol": "freedom",
        "settings": {
          "domainStrategy": "AsIs",
          "fragment": {
            "packets": "tlshello",
            "length": "100-200",
            "interval": "10-20",
            "maxSplit": "300-400"
          }
        },
        "streamSettings": {
          "sockopt": {
            "tcpKeepAliveIdle": 100,
            "tcpMptcp": true,
            "penetrate": true
          }
        }
      },
      {
        "tag": "noises",
        "protocol": "freedom",
        "settings": {
          "domainStrategy": "AsIs",
          "noises": [
            {
              "type": "rand",
              "packet": "10-20",
              "delay": "10-16",
              "applyTo": "ip"
            }
          ]
        }
      }
    ],
    "policy": {
      "levels": {
        "8": {
          "connIdle": 300,
          "downlinkOnly": 1,
          "handshake": 4,
          "uplinkOnly": 1
        }
      },
      "system": {
        "statsOutboundDownlink": true,
        "statsOutboundUplink": true
      }
    },
    "remarks": "🇳🇱 Nederland - запасной #2 для WiFi и LTE-client-ibg9bgf9@vpn.local_13",
    "routing": {
      "domainStrategy": "AsIs",
      "rules": [
        {
          "network": "tcp,udp",
          "outboundTag": "proxy",
          "type": "field"
        }
      ]
    },
    "stats": {}
  }
]
```

